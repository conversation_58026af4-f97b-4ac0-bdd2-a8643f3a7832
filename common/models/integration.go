package models

import (
	"errors"
	"strings"
	"time"

	"gorm.io/gorm"
)

type IntegrationName string
type IntegrationType string

var ErrStaleIntegration = errors.New("stale creds")

// The IntegrationName variable should be stylized to match what the company lists publicly,
// and the string should be all lowercase.
// The IntegrationType variable should indicate the type of integration.
const (
	// Integration types
	CarrierVerification IntegrationType = "carrierverification"
	CRM                 IntegrationType = "crm"
	CustomerType        IntegrationType = "customer" // Not to conflict with Customer struct
	EmailType           IntegrationType = "email"    // Not to conflict with Email struct
	Pricing             IntegrationType = "pricing"
	Scheduling          IntegrationType = "scheduling"
	TMS                 IntegrationType = "tms"
	LLM                 IntegrationType = "llm"

	// Integration names
	// carrierverification
	FreightValidate IntegrationName = "freightvalidate"
	MyCarrierPortal IntegrationName = "mycarrierportal"
	Highway         IntegrationName = "highway"

	// crm
	HubSpot IntegrationName = "hubspot"

	// customer
	Redwood  IntegrationName = "redwood"
	Trifecta IntegrationName = "trifecta"
	ProdDemo IntegrationName = "proddemo"

	// email
	Gmail   IntegrationName = "gmail"
	Outlook IntegrationName = "outlook"
	Front   IntegrationName = "front"

	// llms
	OpenAI IntegrationName = "openAI"

	// pricing
	DAT           IntegrationName = "dat"
	GlobalTranz   IntegrationName = "globaltranz"
	Greenscreens  IntegrationName = "greenscreens"
	TaiPricing    IntegrationName = "tai"
	Truckstop     IntegrationName = "truckstop"
	ThreeGPricing IntegrationName = "threeg"

	// quoting/bidding portals; may overlap with TMSes but we differentiate between them
	FreightView   IntegrationName = "freightview"
	E2OpenQuoting IntegrationName = "e2open-quoting"
	UberFreight   IntegrationName = "uberfreight"

	// scheduling
	C3Reservations IntegrationName = "c3reservations"
	DataDocks      IntegrationName = "datadocks"
	DaySmart       IntegrationName = "daysmart"
	E2open         IntegrationName = "e2open"
	OneNetwork     IntegrationName = "onenetwork"
	Manhattan      IntegrationName = "manhattan"
	Costco         IntegrationName = "costco"
	Lineage        IntegrationName = "lineage"
	Opendock       IntegrationName = "opendock"
	Retalix        IntegrationName = "retalix"
	Velostics      IntegrationName = "velostics"
	YardView       IntegrationName = "yardview"
	EmailRequest   IntegrationName = "email"

	// tms
	Aljex            IntegrationName = "aljex"
	Ascend           IntegrationName = "ascend"
	FreightFlow      IntegrationName = "freightflow"
	Mcleod           IntegrationName = "mcleod"
	McleodEnterprise IntegrationName = "mcleodenterprise"
	GlobalTranzTMS   IntegrationName = "globaltranztms"
	MercuryGate      IntegrationName = "mercurygate"
	PLS              IntegrationName = "pls" // Mock TMS in order to support PLS QQ customer assignment
	Relay            IntegrationName = "relay"
	Revenova         IntegrationName = "revenova"
	Tai              IntegrationName = "tai"
	ThreePLSystems   IntegrationName = "threeplsystems"
	ThreeG           IntegrationName = "threeg"
	TruckMate        IntegrationName = "truckmate"
	Turvo            IntegrationName = "turvo" // and scheduling
	Webhook          IntegrationName = "webhook"
	None             IntegrationName = "none"
	QuantumEdge      IntegrationName = "quantumedge"
	Stark            IntegrationName = "stark"
)

type PricingOnBoardResp struct {
	EncryptedPassword         []byte
	AccessToken               string
	RefreshToken              string
	APIKey                    string
	Username                  string
	AppID                     string
	Note                      string
	AccessTokenExpirationDate NullTime
	ClientID                  string
	ClientSecret              string
}

type Integration struct {
	gorm.Model
	Name                       IntegrationName `gorm:"index" json:"name" validate:"required"`
	Type                       IntegrationType `gorm:"index" json:"type" validate:"required"`
	ServiceID                  uint            `gorm:"index" json:"serviceID" validate:"required"`
	Service                    Service         `json:"-"`
	AppID                      string          `json:"appID"`
	Username                   string          `json:"username"`
	EncryptedPassword          []byte          `gorm:"type:bytea" json:"encryptedPassword"`
	AccessToken                string          `json:"accessToken"`
	AccessTokenExpirationDate  NullTime        `json:"accessTokenExpirationDate"`
	RefreshToken               string          `json:"refreshToken"`
	Disabled                   bool            `gorm:"default:false" json:"disabled"`
	Tenant                     string          `json:"tenant"`
	APIKey                     string          `json:"apiKey"`
	Note                       string          `json:"note"`
	LastCustomerUpdatedAt      time.Time       `json:"lastCustomerUpdatedAt"`
	LastLocationUpdatedAt      time.Time       `json:"lastLocationUpdatedAt"`
	LastCommodityUpdatedAt     time.Time       `json:"lastCommodityUpdatedAt"`
	TMSPollerCustomersDisabled bool            `gorm:"default:false" json:"tmsPollerCustomersDisabled"`
	TMSPollerLocationsDisabled bool            `gorm:"default:false" json:"tmsPollerLocationsDisabled"`
	IsServiceWide              bool            `gorm:"default:true" json:"isServiceWide"`
	UserGroups                 []UserGroup     `gorm:"many2many:user_group_integrations;"`
	TwoFactorSecret            string          `json:"twoFactorSecret"`
	ClientID                   string          `json:"clientID"`
	ClientSecret               string          `json:"clientSecret"`

	// Defined here instead of in service to allow TMS functions to access without loading the service
	FeatureFlags IntegrationFeatureFlags `gorm:"embedded" json:"featureFlags"`
}

type TMSSyncApproach string

const (
	TMSSyncApproachNotSupported TMSSyncApproach = "not_supported"
	TMSSyncApproachPartial      TMSSyncApproach = "partial_sync"
	TMSSyncApproachFull         TMSSyncApproach = "full_sync"
)

type TMSCustomerSyncApproach struct {
	Approach TMSSyncApproach
}

type TMSLocationSyncApproach struct {
	Approach TMSSyncApproach
}

type IntegrationFeatureFlags struct {
	// Some services want to restrict access to write to certain TMS fields,
	// e.g. Mcleod supports assigning carrier but org doesn't want to allow users to do so via Drumkit
	// TODO (future): Add group-level permissions (e.g. sales can't change carrier, etc)
	IsCarrierAssignmentDisabled bool `json:"isCarrierAssignmentDisabled"`
	// This is a McLeod Enterprise feature flag to only require city and state for pickup and consignee.
	// For example, Fetch Freight only requires city and state for pickup and consignee
	// but Trident requires the entire address/location profile
	IsOnlyCityStateRequired bool `gorm:"default:false" json:"isOnlyCityStateRequired"`
	// Some services require revenue code to be filled out on the load,
	// e.g. Trident and Fetch Freight do but Syfan and Tumalo don't
	IsRevenueCodeRequired bool `gorm:"default:false" json:"isRevenueCodeRequired"`
}

// PartialLoadUpdateSupportList contains TMS integrations that support partial updates of loads.
// These integrations can update specific fields without requiring the entire load object to be sent.
var PartialLoadUpdateSupportList = []IntegrationName{
	Aljex,          // Preserves existing form data, only updates changed fields
	GlobalTranzTMS, // Fetches current load first, then applies updates
	Relay,          // Checks for diffs before updating, only calls update functions for changed fields
	Revenova,       // Fetches current load first, preserves unchanged fields when building update request
	Tai,            // Has separate update functions for different field groups (tracking, references, etc.)
	Turvo,          // Explicitly documented to support partial updates, checks for diffs before updating
}

// Integration fields for API responses to frontend
type IntegrationCore struct {
	ID                       uint                    `json:"id"`
	Name                     IntegrationName         `json:"name"`
	Type                     IntegrationType         `json:"type"`
	Username                 string                  `json:"username"`
	Tenant                   string                  `json:"tenant"`
	Note                     string                  `json:"note"`
	FeatureFlags             IntegrationFeatureFlags `json:"featureFlags"`
	CustomerSyncApproach     TMSSyncApproach         `json:"customerSyncApproach"`
	LocationSyncApproach     TMSSyncApproach         `json:"locationSyncApproach"`
	ImplementsCreateLocation bool                    `json:"implementsCreateLocation"`
}

func (i Integration) NeedsRefresh() bool {
	now := time.Now()
	return !i.AccessTokenExpirationDate.Valid ||
		now.After(i.AccessTokenExpirationDate.Time) ||
		now.Add(24*time.Hour).After(i.AccessTokenExpirationDate.Time)
}

// SupportsPartialLoadUpdates determines if a TMS integration supports partial load updates.
func SupportsPartialLoadUpdates(name IntegrationName) bool {
	for _, supportedName := range PartialLoadUpdateSupportList {
		if name == supportedName {
			return true
		}
	}

	return false
}

// FormatIntegrationName formats the integration name for user-facing strings, using the brand's styling where possible
// (e.g. opendock -> OpenDock).
func FormatIntegrationName(name IntegrationName) string {
	switch name {
	// For user-facing messages, don't need to differentiate between "Mcleod" and "Mcleod Enterprise"
	case McleodEnterprise, Mcleod:
		return "Mcleod"
	case DAT:
		return "DAT"
	case GlobalTranz:
		return "Global Tranz"
	case Greenscreens:
		return "Greenscreens"
	case TruckMate:
		return "TruckMate"
	case Tai:
		return "Tai"
	case ThreePLSystems:
		return "3PL"
	case Turvo:
		return "Turvo"
	case OpenAI:
		return "OpenAI"
	default:
		str := strings.TrimSpace(string(name))
		if len(str) < 2 {
			return str
		}

		return strings.ToUpper(string(str[0])) + strings.ToLower(str[1:])
	}
}

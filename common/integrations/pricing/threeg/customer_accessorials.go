package threeg

import (
	"context"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

func (c *Client) GetCustomerAccessorials(
	ctx context.Context,
	customerOrgID,
	brokerID,
	shipmentType int,
) ([]models.Accessorial, error) {
	return nil, helpers.NotImplemented(models.ThreeG, "GetCustomerAccessorials")
}

func (c *Client) GetStaticCustomerAccessorials() []models.Accessorial {
	// Not implemented
	return nil
}

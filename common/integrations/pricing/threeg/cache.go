package threeg

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

const redisKeyTTL = 30 * time.Minute

func getKey(email string) string {
	return fmt.Sprintf("tai-quoting-email-%s", strings.ToLower(email))
}

func getCachedClient(ctx context.Context, userName string) *Client {
	c, found, err := redis.GetKey[Client](ctx, getKey(userName))
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "failed to get Tai client from Redis", zap.Error(err))
	}

	if found {
		log.Info(ctx, "re-using existing Tai client")
		c.HTTPClient = &http.Client{}
		return &c
	}

	return nil
}

func (c *Client) cache(ctx context.Context) error {
	key := get<PERSON><PERSON>(c.<PERSON>)

	err := redis.Set<PERSON>ey(ctx, key, c, redisKeyTTL)
	if err != nil {
		return errors.New("failed to cache Tai client")
	}

	return nil
}

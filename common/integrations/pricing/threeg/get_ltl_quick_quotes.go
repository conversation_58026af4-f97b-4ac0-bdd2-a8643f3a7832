package threeg

import (
	"context"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

type QuickQuoteLineItem struct {
	PackageType                      int      `json:"packageType"`
	FreightClass                     string   `json:"freightClass"`
	ClassManuallySelected            bool     `json:"classManuallySelected"`
	CubicFeet                        string   `json:"cubicFeet"`
	CubicMeters                      string   `json:"cubicMeters"`
	PoundsPerCubicFeet               string   `json:"poundsPerCubicFeet"`
	DimensionalWeight                *int     `json:"dimensionalWeight"`
	ChargeableWeight                 *int     `json:"chargeableWeight"`
	EstimatedFreightClass            string   `json:"estimatedFreightClass"`
	ShowEstimatedFreightClassMessage bool     `json:"showEstimatedFreightClassMessage"`
	CubicFeetChanged                 bool     `json:"cubicFeetChanged"`
	QuantityOfGoods                  int      `json:"quantityOfGoods"`
	TotalWeight                      int      `json:"totalWeight"`
	CommodityDescription             string   `json:"commodityDescription"`
	HandlingUnits                    int      `json:"handlingUnits"`
	SizeLength                       *float32 `json:"sizeLength"`
	SizeWidth                        *float32 `json:"sizeWidth"`
	SizeHeight                       *float32 `json:"sizeHeight"`
}

type LTLQuoteRequest struct {
	ShipmentID                   int                  `json:"shipmentId"`
	OriginCity                   string               `json:"originCity"`
	OriginState                  string               `json:"originState"`
	OriginZip                    string               `json:"originZip"`
	OriginCountry                string               `json:"originCountry"`
	LineItems                    []QuickQuoteLineItem `json:"lineItems"`
	BrokerID                     int                  `json:"brokerId"`
	CustomerStaffID              int                  `json:"customerStaffId"`
	DestinationCity              string               `json:"destinationCity"`
	DestinationState             string               `json:"destinationState"`
	DestinationZip               string               `json:"destinationZip"`
	DestinationCountry           string               `json:"destinationCountry"`
	WeightUnit                   int                  `json:"weightUnit"`
	DimensionUnit                int                  `json:"dimensionUnit"`
	SearchByTariff               bool                 `json:"searchByTariff"`
	Stackable                    bool                 `json:"stackable"`
	ShipmentType                 int                  `json:"shipmentType"`
	ActualShipmentType           int                  `json:"actualShipmentType"`
	TrailerType                  int                  `json:"trailerType"`
	PickupReadyDateTime          string               `json:"pickupReadyDateTime"`
	PickupCloseDateTime          string               `json:"pickupCloseDateTime"`
	PickupAppointmentBeginTime   *string              `json:"pickupAppointmentBeginTime"`
	PickupAppointmentEndTime     *string              `json:"pickupAppointmentEndTime"`
	DeliveryReadyDateTime        *string              `json:"deliveryReadyDateTime"`
	DeliveryCloseDateTime        *string              `json:"deliveryCloseDateTime"`
	DeliveryAppointmentBeginTime *string              `json:"deliveryAppointmentBeginTime"`
	DeliveryAppointmentEndTime   *string              `json:"deliveryAppointmentEndTime"`
	Mileage                      float64              `json:"mileage"`
	Accessorials                 []int                `json:"accessorials"`
	IsFrontOffice                bool                 `json:"isFrontOffice"`
	DeclaredValue                string               `json:"declaredValue"`
	DeductibleValue              float64              `json:"deductibleValue"`
	LinearFeet                   *int                 `json:"linearFeet"`
}

func (c *Client) GetLTLQuickQuotes(ctx context.Context, req LTLQuoteRequest) ([]models.QuickQuote, error) {
	return nil, helpers.NotImplemented(models.ThreeG, "GetLTLQuickQuotes")
}

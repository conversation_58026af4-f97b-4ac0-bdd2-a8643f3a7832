package threeg

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	API interface {
		SearchCustomers(ctx context.Context, query string) ([]models.TMSCustomer, error)
		SearchCustomerStaff(
			ctx context.Context,
			customerOrganizationID int,
			customerOrganizationName string,
			enabled bool,
		) ([]models.TMSCustomer, error)

		GetCustomerAccessorials(
			ctx context.Context,
			customerOrgID,
			brokerID,
			shipmentType int,
		) ([]models.Accessorial, error)
		GetStaticCustomerAccessorials() []models.Accessorial

		GetMileageByZipCodes(
			ctx context.Context,
			customerOrganizationID int,
			originZip,
			originCountry,
			destinationZip,
			destinationCountry string,
			pcmilerRoutingType int,
		) (float64, error)

		GetLTLQuickQuotes(
			ctx context.Context,
			req LTLQuoteRequest,
		) ([]models.QuickQuote, error)

		GetStaticServiceLevels() []string
	}

	Client struct {
		Integration models.Integration `json:"-"`
		UserName    string             `json:"userName"`
		Cookies     []*http.Cookie     `json:"cookies"`
		HTTPClient  *http.Client       `json:"-"`
		WebBaseURL  string             `json:"webBaseURL"`
	}
)

func New(ctx context.Context, integration models.Integration) (*Client, error) {
	username := integration.Username
	password := integration.EncryptedPassword

	cachedClient := getCachedClient(ctx, username)

	if cachedClient != nil {
		cachedClient.Integration = integration
		return cachedClient, nil
	}

	hostname := getHostname(integration.Tenant)
	webBaseURL := fmt.Sprintf("https://%s", hostname)

	if username == "" || password == nil {
		return nil, errors.New("missing Tai username or password")
	}

	client := &Client{
		UserName:    username,
		Integration: integration,
		WebBaseURL:  webBaseURL,
	}

	if err := client.login(ctx); err != nil {
		return nil, fmt.Errorf("error authenticating with 3G TMS: %w", err)
	}

	err := client.cache(ctx)
	if err != nil {
		log.Warn(ctx, "failed to set Tai client in Redis", zap.Error(err))
	}

	return client, nil
}

func (c Client) get(ctx context.Context, path string, query url.Values, out any) (helpers.APIResponse, error) {
	return c.do(ctx, http.MethodGet, path, query, nil, out)
}

func (c Client) post(ctx context.Context, path string, body io.Reader, out any) (helpers.APIResponse, error) {
	return c.do(ctx, http.MethodPost, path, nil, body, out)
}

func (c Client) do(
	ctx context.Context,
	method,
	path string,
	query url.Values,
	reqBody io.Reader,
	out any,
) (helpers.APIResponse, error) {

	url := (&url.URL{
		Scheme:   "https",
		Host:     c.Integration.Tenant,
		Path:     path,
		RawQuery: query.Encode(),
	}).String()

	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return helpers.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to build %s %s request: %w", method, url, err)
	}

	if method == http.MethodPost || method == http.MethodPut {
		req.Header.Set("Content-Type", "application/json")
	}

	req.Header.Set("Accept", "application/json")

	if len(c.Cookies) > 0 {
		for _, ck := range c.Cookies {
			if ck != nil && ck.Name != "" {
				req.AddCookie(ck)
			}
		}
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, c.Integration, err)
		return helpers.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to send %s %s request: %w", method, url, err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, c.Integration, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return helpers.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to read %s response body: %w", url, err)
	}

	if code := resp.StatusCode; code != http.StatusOK && code != http.StatusCreated {
		return helpers.APIResponse{Status: resp.StatusCode, Body: string(body)},
			errtypes.NewHTTPResponseError(c.Integration, req, resp, body)
	}

	if out != nil {
		if err := json.Unmarshal(body, out); err != nil {
			log.Error(ctx, "json unmarshal failed for Tai response body", zap.ByteString("body", body))

			return helpers.APIResponse{Status: resp.StatusCode, Body: string(body)},
				fmt.Errorf("%s %s json unmarshal failed: %w", method, url, err)
		}

		if path != loginEndpoint {
			log.Debug(
				ctx,
				"received Tai response",
				zap.String("method", method),
				zap.String("url", url),
				zap.Any("body", out),
			)
		}
	}

	return helpers.APIResponse{Status: resp.StatusCode, Body: string(body)}, nil
}
